<?php

namespace App\Fields\PostTypes;

use App\Fields\Partials\Components\BigNumber;
use App\Fields\Partials\Components\CenteredImage;
use App\Fields\Partials\Components\Quote;
use App\Fields\Partials\Components\RelatedNews;
use App\Fields\Partials\Components\RelatedPublications;
use App\Fields\Partials\Components\Takeaways;
use App\Fields\Partials\Components\TextContent;
use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Post extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('post');

        $fields
            ->setLocation('post_type', '==', 'post');

        $fields
            ->addTrueFalse('is_featured', [
                'label' => __('Featured', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addFlexibleContent('sections')
                ->addLayout('takeaways_layout', [
                    'label' => __('Takeaways', 'abbl-backend'),
                ])
                    ->addPartial(Takeaways::class)
                ->addLayout('centered_image_layout', [
                    'label' => __('Centered image', 'abbl-backend'),
                ])
                    ->addPartial(CenteredImage::class)
                ->addLayout('text_content_layout', [
                    'label' => __('Text content', 'abbl-backend'),
                ])
                    ->addPartial(TextContent::class)
                ->addLayout('quote_layout', [
                    'label' => __('Quote', 'abbl-backend'),
                ])
                    ->addPartial(Quote::class)
                ->addLayout('related_publications_layout', [
                    'label' => __('Related publications', 'abbl-backend'),
                ])
                    ->addPartial(RelatedPublications::class)
                ->addLayout('related_news_layout', [
                    'label' => __('Related news', 'abbl-backend'),
                ])
                    ->addPartial(RelatedNews::class)
                ->addLayout('big_number_layout', [
                    'label' => __('Big number', 'abbl-backend'),
                ])
                    ->addPartial(BigNumber::class)
            ->endFlexibleContent();

        return $fields->build();
    }
}

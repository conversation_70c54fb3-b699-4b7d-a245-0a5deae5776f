<?php

namespace App\Modules;

use Carbon\Carbon;

class EventsModule
{
    public static array $taxonomies = [
        TAX_EVENT_CATEGORY,
        TAX_EVENT_ORGANIZER,
        TAX_EVENT_VENUE,
        TAX_EVENT_FORMAT,
        TAX_EVENT_LANGUAGE,
    ];

    public static function bootstrap()
    {
        add_filter('query_vars', [self::class, 'queryVars']);

        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        register_extended_post_type(CPT_EVENT, [
            'menu_position' => 4,
            'has_archive' => true,
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-calendar-alt',
            'supports' => ['title', 'editor', 'thumbnail', 'excerpt'],
            'taxonomies' => [],
            'admin_cols' => [
                'is_featured' => [
                    'title' => 'Featured',
                    'render' => function ($post) {
                        return get_field('is_featured', $post->ID) ? 'Yes' : 'No';
                    },
                ],
                'event_category' => [
                    'taxonomy' => TAX_EVENT_CATEGORY,
                    'title' => 'Event category',
                ],
                'event_organizer' => [
                    'taxonomy' => TAX_EVENT_ORGANIZER,
                    'title' => 'Organizer',
                ],
                'event_venue' => [
                    'taxonomy' => TAX_EVENT_VENUE,
                    'title' => 'Venue',
                ],
            ]
        ], [
            'slug' => 'events',
        ]);

        register_extended_taxonomy(TAX_POST_CONTENT_TYPE, CPT_EVENT, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Content types',
            'singular' => 'Content type',
        ]);

        register_extended_taxonomy(TAX_EVENT_CATEGORY, CPT_EVENT, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Event categories',
            'singular' => 'Event category',
        ]);

        register_extended_taxonomy(TAX_EVENT_ORGANIZER, CPT_EVENT, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Organizers',
            'singular' => 'Organizer',
        ]);

        register_extended_taxonomy(TAX_EVENT_VENUE, CPT_EVENT, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Venues',
            'singular' => 'Venue',
        ]);

        register_extended_taxonomy(TAX_EVENT_FORMAT, CPT_EVENT, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Formats',
            'singular' => 'Format',
        ]);

        register_extended_taxonomy(TAX_EVENT_LANGUAGE, CPT_EVENT, [], [
            'plural' => 'Languages',
            'singular' => 'Language',
        ]);
    }

    public static function queryVars($query_vars) {
        foreach (EventsModule::$taxonomies as $taxonomy) {
            $query_vars[] = $taxonomy;
        }

        $query_vars[] = 'event-date';

        return $query_vars;
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = CPT_EVENT;
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;
        $query['tax_query'] = $query['tax_query'] ?? [];

        $ignore_featured = $query['ignore_featured'] ?? false;

        foreach (EventsModule::$taxonomies as $taxonomy) {
            if ($term = get_query_var($taxonomy)) {
                $query['tax_query'][] = [
                    'taxonomy' => $taxonomy,
                    'field' => 'slug',
                    'terms' => $term,
                ];
            }
        }

        $event_date_var = get_query_var('event-date');

        if (preg_match('/^\d{2}-\d{4}$/', $event_date_var)) {
            [$month, $year] = explode('-', $event_date_var);

            $date = Carbon::createFromDate(year: $year, month: $month);

            $query['meta_query'] = [
                'relation' => 'AND',
                [
                    'key' => 'start_datetime',
                    'value' => $date->format('Y-m-d'),
                    'compare' => '>=',
                    'type' => 'DATE',
                ],
                [
                    'key' => 'start_datetime',
                    'value' => $date->addMonth()->format('Y-m-d'),
                    'compare' => '<',
                    'type' => 'DATE',
                ],
            ];
        }

        if (!$ignore_featured) {
            $query['orderby'] = array_merge(['is_featured' => 'DESC'], $query['orderby'] ?? []);
        }

        return new \WP_Query($query);
    }
}

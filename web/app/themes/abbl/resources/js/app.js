import.meta.glob([
  '../images/**',
  '../fonts/**',
]);

import Swiper from 'swiper';
import {A11y, Pagination, Navigation} from 'swiper/modules';

/**
 * Create a range of numbers.
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#sequence_generator_range
 * @param {number} start The first number in the range.
 * @param {number} stop The last number in the range.
 * @param {number} step The step between each number in the range.
 * @returns {number[]} A range of numbers.
 */
const range = (start, stop, step = 1) => Array.from(
    { length: (stop - start) / step + 1 },
    (_, i) => start + i * step
);

const fadeHooks = {
    afterInit: function () {
        range(this.activeIndex, this.activeIndex + 2).forEach(i => {
            this.slides[i].classList.add('active');
        });
    },
    slideChange: function () {
        this.slides.forEach(s => s.classList.remove('active'));

        range(this.activeIndex, this.activeIndex + 2).forEach(i => {
            this.slides[i].classList.add('active');
        });
    }
};

function createBasicSlider(selector) {
    return new Swiper(`${selector} .swiper`, {
        modules: [A11y, Pagination, Navigation],
        slidesPerView: 1,
        spaceBetween: 36,
        loop: true,
        pagination: {
            el: `${selector} .swiper-pagination`,
            clickable: true,
        },
        navigation: {
            nextEl: `${selector} .swiper-button-next`,
            prevEl: `${selector} .swiper-button-prev`,
        },
        on: fadeHooks,
        breakpoints: {
            1200: {
                slidesPerView: 3,
                spaceBetween: 20,
            },
        },
    });
}

function homeHotTopicsSlider() {
    const id = '#home-hot-topics-slider';

    const listItems = Array.from(document.querySelectorAll('#slider-hot-topics-list li'));

    const slider = new Swiper(`${id} .swiper`, {
        modules: [A11y, Pagination, Navigation],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        pagination: {
            el: `${id} .swiper-pagination`,
            clickable: true,
        },
        navigation: {
            nextEl: `${id} .swiper-button-next`,
            prevEl: `${id} .swiper-button-prev`,
        },
        on: {
            afterInit: function () {
                this.slides[this.activeIndex].classList.add('active');

                listItems.forEach(item => {
                    const index = Number(item.dataset.index);

                    item.addEventListener('click', () => {
                        listItems.forEach(i => i.classList.remove('font-bold'));

                        item.classList.add('font-bold');

                        slider.slideTo(index);
                    });
                });
            },
            slideChange: function () {
                this.slides.forEach(s => s.classList.remove('active'));

                this.slides[this.activeIndex].classList.add('active');

                listItems.forEach(item => item.classList.remove('font-bold'));

                listItems[this.activeIndex].classList.add('font-bold');
            },
        },
    });
}

function archiveFilters() {
    const elements = Array.from(document.querySelectorAll('.archive-filters'));

    elements.forEach(element => {
        const toggle = element.querySelector('.archive-filters-toggle');

        toggle.addEventListener('click', () => {
            console.log('ok')
            element.classList.toggle('archive-filters--open');
        });
    });
}

function siteSectionSwitcher() {
    const professionalsButton = document.querySelector('#header-professionals-button');
    const consumersButton = document.querySelector('#header-consumers-button');

    professionalsButton.addEventListener('click', (e) => {
        e.preventDefault();

        window.localStorage.setItem('abblUserType', 'professional');

        window.location = e.target.href;
    });

    consumersButton.addEventListener('click', (e) => {
        e.preventDefault();

        window.localStorage.setItem('abblUserType', 'consumer');

        window.location = e.target.href;
    });
}

function mainMenu() {
    const body = document.querySelector('body');
    const primaryMenuButton = document.querySelector('#primary-menu-button');
    const subMenuInfos = document.querySelector('#sub-menu-infos');
    const subMenuClose = document.querySelector('#sub-menu-infos-close');
    const subMenuLabels = document.querySelector('#sub-menu-infos-labels');

    primaryMenuButton.addEventListener('click', () => {
        body.classList.toggle('is-primary-menu-active');
    });

    const firstLevelMenus = Array.from(document.querySelectorAll('#primary-menu > li.menu-item-has-children'));
    const allSubMenus = Array.from(document.querySelectorAll('#primary-menu > li.menu-item-has-children > ul'));

    firstLevelMenus.forEach((el) => {
        const link = el.querySelector('a');
        const subMenu = el.querySelector('ul');

        link.addEventListener('click', (e) => {
            e.preventDefault();

            subMenu.classList.toggle('is-open');

            updateInfos();

            return false;
        });
    });

    function updateInfos() {
        const activeSubMenus = document.querySelectorAll('#primary-menu > li.menu-item-has-children > ul.is-open');

        subMenuInfos.classList.toggle('hidden', activeSubMenus.length === 0);

        const ancestor = document.querySelector('#primary-menu > li.current-menu-ancestor');

        if (ancestor) {
            subMenuLabels.innerHTML = `<span>Home /</span> <span>${ancestor.querySelector('a').innerText}</span>`;
        }
    }

    subMenuClose.addEventListener('click', () => {
        allSubMenus.forEach((el) => el.classList.remove('is-open'));

        updateInfos();
    });
}

document.addEventListener('DOMContentLoaded', () => {
    createBasicSlider('#home-news-slider');
    createBasicSlider('#home-publications-slider');
    homeHotTopicsSlider();

    archiveFilters();

    siteSectionSwitcher();

    mainMenu();
});

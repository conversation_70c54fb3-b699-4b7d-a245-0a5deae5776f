{{--
    Template Name: Professionals homepage
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post() @endphp

        <x-featured-hot-topics-slider/>

        <x-section class="py-16 my-0 bg-light-blue">
            <x-container class="relative grid lg:grid-cols-[2fr_1fr] lg:items-center lg:gap-24">
                <div class="space-y-4">
                    <h2 class="mb-2.5 text-[0.9375rem] font-bold uppercase">ABBL - The Luxembourg’s Bankers’ Association</h2>
                    <p class="text-[1.25rem] font-headings font-bold">Empowering global competitiveness, advancing prosperity.</p>
                    <div class="wysiwyg">
                        <p>At the heart of Europe, Luxembourg’s dynamic financial centre drives  sustainable growth—empowering businesses to compete globally and  fostering long-term prosperity. The Association des Banques et  Banquiers, Luxembourg (ABBL) champions this vision by promoting  regulated, innovative, and responsible financial services.</p>
                    </div>
                    <ul class="flex justify-between lg:justify-start lg:items-center lg:gap-14 my-12">
                        @php
                        $items = [
                            ['number' => '250+', 'title' => 'members'],
                            ['number' => '2,500', 'title' => 'experts'],
                            ['number' => '85 years', 'title' => 'of experience'],
                        ];
                        @endphp

                        @foreach($items as $item)
                            <div>
                                <p class="text-[1.5rem] leading-none font-bold">{{ $item['number'] }}</p>
                                <p class="text-[0.875rem] uppercase">{{ $item['title'] }}</p>
                            </div>
                            @if(!$loop->last)
                                <div class="w-0.5 h-8 bg-navy-blue"></div>
                            @endif
                        @endforeach
                    </ul>
                    <div class="grid grid-cols-2 lg:flex gap-8">
                        <x-link-button href="#">
                            {{ __('Become a member', 'abbl') }}
                        </x-link-button>
                        <x-link-button href="#" class="bg-white">
                            {{ __(' Learn more about ABBL', 'abbl') }}
                        </x-link-button>
                    </div>
                </div>
                <div class="py-18 px-14 rounded-lg bg-navy-blue">
                    <h3 class="mb-8 text-[1.75rem] font-bold text-neon-green">{{ __('Quick access', 'abbl') }}</h3>
                    <ul class="grid gap-5">
                        @php
                        $links = [
                            ['icon' => 'far fa-bank', 'title' => 'Bank Holidays', 'url' => '#'],
                            ['icon' => 'far fa-bookmark', 'title' => 'Publications', 'url' => '#'],
                            ['icon' => 'far fa-user-group', 'title' => 'Consumers area', 'url' => '#'],
                            ['icon' => 'far fa-briefcase', 'title' => 'Jobs and Careers', 'url' => '#'],
                        ];
                        @endphp

                        @foreach($links as $link)
                            <li>
                                <a href="{{ $link['url'] }}" class="flex items-center gap-4 pl-5 pr-2 py-4 rounded font-medium bg-white">
                                    <i class="{{ $link['icon'] }}"></i>
                                    <span>{{ $link['title'] }}</span>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </x-container>
        </x-section>

        <div class="py-20 bg-abbl-gradient-grain">
            <x-section class="mt-0">
                <x-container>
                    <h2 class="mb-8 font-headings text-[1.75rem] text-white font-bold">
                        {{ __('Our latest news', 'abbl') }}
                    </h2>

                    <x-news-slider />

                    <div class="flex justify-center lg:justify-start mt-5">
                        <x-link-button href="{{ get_post_type_archive_link('post') }}">
                            {{ __('More news & insights', 'abbl') }}
                        </x-link-button>
                    </div>
                </x-container>
            </x-section>

            <x-section>
                <x-container class="relative grid lg:grid-cols-[2fr_1fr] lg:gap-16">
                    {{-- START EVENTS --}}
                    <div>
                        <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-white font-bold">{{ __('Upcoming events', 'abbl') }}</h2>

                        @php
                        $events_query = new WP_Query([
                            'post_type' => CPT_EVENT,
                            'posts_per_page' => 3,
                        ]);

                        $i = 0;
                        @endphp

                        @if($events_query->have_posts())
                            <div id="events-grid" class="grid lg:grid-cols-2 gap-4">
                                @while($events_query->have_posts())
                                    @php $events_query->the_post(); @endphp

                                    <x-event-card
                                        :featured="$events_query->current_post === 0"
                                        @class([
                                            'glow-neon-green border border-neon-green lg:col-span-2' => $i === 0,
                                            'relative z-10' => $i > 0
                                        ])
                                    />

                                    @php $i++; @endphp
                                @endwhile
                            </div>
                            <div class="mt-14">
                                <x-link-button href="{{ get_post_type_archive_link(CPT_EVENT) }}">
                                    {{ __('All upcoming events', 'abbl') }}
                                </x-link-button>
                            </div>
                        @else
                            <p>{{ __('No upcoming events.') }}</p>
                        @endif

                        @php wp_reset_postdata(); @endphp
                    </div>
                    {{-- END EVENTS --}}

                    {{-- START LINKEDIN FEED --}}
                    <div>
                        <h2 class="mb-8 font-headings text-[1.75rem] text-white font-bold">{{ __('ABBL on LinkedIn', 'abbl') }}</h2>
                        <div id="linkedin-feed" class="overflow-y-auto space-y-4 p-4 rounded-lg bg-light-blue">
                            @foreach(range(0, 3) as $i)
                                <div class="px-5 py-4 rounded-lg text-black bg-white">
                                    <div class="flex items-center gap-4 mb-4">
                                        <div class="aspect-square flex justify-center items-center w-14 p-2 rounded-full bg-abbl-gradient">
                                            <img src="{{ Vite::asset('resources/images/abbl-logo.svg') }}" alt="">
                                        </div>
                                        <div>
                                            <h3 class="font-bold">ABBL</h3>
                                            <p class="text-[0.875rem]">Published on 10.20.2025</p>
                                        </div>
                                        <div class="ml-auto self-start">
                                            <i class="fab fa-linkedin text-[1.5rem] text-[#1E66BF]"></i>
                                        </div>
                                    </div>
                                    <div class="mb-6 space-y-[0.875rem] text-[0.875rem]">
                                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer mattis massa quis orci congue laoreet. Nam eu nibh id tortor placerat venenatis in ac quam. Integer nec est enim.</p>
                                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer mattis massa quis orci congue laoreet.</p>
                                    </div>
                                    <div>
                                        <img src="{{ Vite::asset('resources/images/needs-integration/linkedin-feed-image.jpg') }}" alt="" class="rounded">
                                    </div>
                                    <div class="h-px my-5 bg-[#EAEAEA]"></div>
                                    <div class="flex text-[0.875rem] [&>*]:flex [&>*]:items-center [&>*]:gap-2">
                                        <a href="#" class="mr-7 text-[#8E8E8E]">
                                            <i class="fas fa-heart text-[1rem]"></i>
                                            <span>468</span>
                                        </a>
                                        <a href="#" class="text-[#8E8E8E]">
                                            <i class="fab fa-rocketchat text-[1rem]"></i>
                                            <span>12</span>
                                        </a>
                                        <a href="#" class="ml-auto">
                                            <span>Share</span>
                                            <i class="fas fa-share text-[1rem]"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <div class="flex justify-center lg:justify-start mt-14">
                            <x-link-button href="#">{{ __('Follow ABBL on LinkedIn', 'abbl') }}</x-link-button>
                        </div>
                    </div>
                    {{-- END LINKEDIN FEED --}}

                    <div
                        class="absolute -left-[24.4375rem] top-[11.25rem] w-[12.375rem] h-[16.3125rem] hidden lg:block point-events-none bg-fit bg-center"
                        style="background-image: url('{{ Vite::asset('resources/images/decorations/percentage.svg') }}')"
                    ></div>
                </x-container>
            </x-section>

            {{-- START PUBLICATIONS --}}
            <x-section class="mb-0">
                <x-container>
                    <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-white font-bold">
                        {{ __('Our latest publications', 'abbl') }}
                    </h2>

                    @php
                    $publications_query = new WP_query([
                        'post_type' => CPT_PUBLICATION,
                        'posts_per_page' => 6,
                    ]);
                    @endphp

                    <div id="home-publications-slider" class="swiper-container with-fade">
                        <!-- Slider main container -->
                        <div class="swiper">
                            <!-- Additional required wrapper -->
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                @while($publications_query->have_posts())
                                    @php $publications_query->the_post(); @endphp

                                    <div class="swiper-slide">
                                        <x-publication-card :featured="$publications_query->current_post === 0" @class(['h-full', 'border border-neon-green glow-neon-green' => $publications_query->current_post === 0, 'relative z-10' => $publications_query->current_post > 0]) />
                                    </div>
                                @endwhile
                            </div>
                        </div>

                        <div class="swiper-pagination"></div>

                        <!-- If we need navigation buttons -->
                        <div class="swiper-button-prev">
                            <i class="fas fa-square-chevron-left text-yellow"></i>
                        </div>
                        <div class="swiper-button-next">
                            <i class="fas fa-square-chevron-right text-yellow"></i>
                        </div>
                    </div>

                    <div class="relative z-10 flex justify-center lg:justify-start mt-5">
                        <x-link-button href="#">{{ __('See all publications', 'abbl') }}</x-link-button>
                    </div>
                </x-container>
            </x-section>

            {{-- END PUBLICATIONS --}}
        </div>
    @endwhile
@endsection

@push('footer-scripts')
    <script>
        const eventsGrid = document.querySelector('#events-grid');
        const linkedinFeed = document.querySelector('#linkedin-feed');
        const feedImages = Array.from(linkedinFeed.querySelectorAll('img'));

        function resizeLinkedInFeedHeight() {
            const eventsGridHeight = eventsGrid.getBoundingClientRect().height;
            linkedinFeed.style.height = `${eventsGridHeight}px`;
        }

        resizeLinkedInFeedHeight();

        window.addEventListener('resize', () => resizeLinkedInFeedHeight());

        feedImages.forEach(img => {
            img.addEventListener('load', () => resizeLinkedInFeedHeight());
        });

        document.addEventListener('load', () => resizeLinkedInFeedHeight());
        document.addEventListener('DOMContentLoaded', () => resizeLinkedInFeedHeight());
    </script>
@endpush

@props([
    'title' => null,
    'badge' => null,
    'bgUrl' => Vite::asset('resources/images/defaults/hero-bg.jpg'),
    'date' => null,
])

<div {{ $attributes->twMerge(['relative py-24 bg-navy-blue']) }}>
    <div class="absolute inset-0 bg-cover bg-center" @if($bgUrl) style="background-image: url('{{ $bgUrl }}')" @endif></div>
    <div class="absolute inset-0 bg-navy-blue/75"></div>
    <div class="absolute inset-0 left-0 w-[66%] bg-linear-to-r from-navy-blue/75 to-transparent"></div>

    <x-container class="relative">
        @if($badge)
            <div class="mb-5">
                <x-badge>{{ $badge }}</x-badge>
            </div>
        @endif
        @if($title)
            <h1 class="max-w-[24.125rem] mb-5 font-headings text-[1.25rem] font-bold text-neon-green">{!! $title !!}</h1>
        @endif
        @if($date)
            <p class="text-[0.9375rem] font-bold text-white">{{ $date }}</p>
        @endif
        <div class="max-w-[600px] text-[0.9375rem] text-white">
            {{ $slot }}
        </div>
    </x-container>
</div>

@props([
    'variant' => 'normal',
    'size' => 'normal',
])

@php
    $sizes = [
        'normal' => 'gap-5 h-[3rem] px-9 font-semibold',
        'sm' => 'gap-3 h-[2.0625rem] px-5 text-[0.875rem] font-medium',
    ];

    $variants = [
        'normal' => "inline-flex justify-center items-center $sizes[$size] rounded text-navy-blue bg-yellow cursor-pointer transition-opacity hover:opacity-75 -outline-offset-3 outline-navy-blue focus-visible:outline-3",
        'outline' => "inline-flex justify-center items-center $sizes[$size] border border-yellow rounded text-navy-blue cursor-pointer -outline-offset-3 outline-navy-blue focus-visible:outline-3",
    ];
@endphp

<a {{ $attributes->twMerge($variants[$variant]) }}>
    {{ $slot }}
</a>

@php use App\Modules\PostsModule; @endphp
@props([
    'postsPerPage' => 6
])

@php
$news_query = PostsModule::query([
    'posts_per_page' => $postsPerPage,
]);
@endphp

<div id="home-news-slider" class="swiper-container with-fade">
    <!-- Slider main container -->
    <div class="swiper">
        <!-- Additional required wrapper -->
        <div class="swiper-wrapper">
            <!-- Slides -->
            @while($news_query->have_posts())
                @php $news_query->the_post(); @endphp

                <div class="swiper-slide">
                    <x-news-card />
                </div>
            @endwhile
        </div>
    </div>

    <div class="swiper-pagination"></div>

    <!-- If we need navigation buttons -->
    <div class="swiper-button-prev">
        <i class="fas fa-square-chevron-left text-yellow"></i>
    </div>
    <div class="swiper-button-next">
        <i class="fas fa-square-chevron-right text-yellow"></i>
    </div>
</div>

@php wp_reset_postdata(); @endphp
